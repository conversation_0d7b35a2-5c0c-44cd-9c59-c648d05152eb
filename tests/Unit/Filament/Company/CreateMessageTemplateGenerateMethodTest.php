<?php

declare(strict_types=1);

use App\Enums\MessageTemplateParameterType;
use App\Filament\Company\Resources\MessageTemplateResource;
use App\Models\Company;
use App\Models\User;
use App\Services\AI\TemplateGenerator;
use Exception;
use Filament\Facades\Filament;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;
use Mockery;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function () {
    seed();

    $this->company = Company::factory()->create();
    $this->user = User::factory()->create();
    $this->company->users()->attach($this->user);
});

afterEach(function () {
    Mockery::close();
});

it('successfully generates template and fills form with content and parameters', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    // Create a mock page instance to test the generateTemplate method directly
    $page = new class extends MessageTemplateResource\Pages\CreateMessageTemplate {
        public $form;
        public $dispatched = [];

        public function __construct()
        {
            // Mock the form
            $this->form = Mockery::mock();
            $this->form->shouldReceive('fill')
                ->once()
                ->with([
                    'content' => 'Hello {{name}}! Welcome to {{company}}.',
                    'parameters' => [
                        [
                            'name' => 'name',
                            'type' => MessageTemplateParameterType::string->value,
                            'max_limit' => 10,
                        ],
                        [
                            'name' => 'company',
                            'type' => MessageTemplateParameterType::string->value,
                            'max_limit' => 10,
                        ],
                    ],
                ]);
        }

        protected function getTemplateGenerator(): TemplateGenerator
        {
            $mock = Mockery::mock(TemplateGenerator::class);
            $mock->shouldReceive('generate')
                ->once()
                ->with('welcome', 'Welcome new users', 'ar')
                ->andReturn([
                    'success' => true,
                    'content' => 'Hello {{name}}! Welcome to {{company}}.',
                    'parameters' => [
                        ['name' => 'name', 'description' => 'User name'],
                        ['name' => 'company', 'description' => 'Company name'],
                    ],
                ]);
            return $mock;
        }

        public function dispatch(string $event, array $data = []): void
        {
            $this->dispatched[] = ['event' => $event, 'data' => $data];
        }

        public function testGenerateTemplate(array $data): void
        {
            $this->generateTemplate($data);
        }
    };

    // Test the generateTemplate method
    $page->testGenerateTemplate([
        'type' => 'welcome',
        'description' => 'Welcome new users',
        'language' => 'ar',
    ]);

    // Verify the dispatch was called correctly
    expect($page->dispatched)->toHaveCount(1);
    expect($page->dispatched[0]['event'])->toBe('template-generated');
    expect($page->dispatched[0]['data'])->toBe([
        'content' => 'Hello {{name}}! Welcome to {{company}}.',
        'parameters' => [
            ['name' => 'name', 'description' => 'User name'],
            ['name' => 'company', 'description' => 'Company name'],
        ],
    ]);
});

it('handles unsuccessful template generation and shows error notification', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $notificationSent = false;

    // Create a mock page instance
    $page = new class($notificationSent) extends MessageTemplateResource\Pages\CreateMessageTemplate {
        private bool &$notificationSent;

        public function __construct(bool &$notificationSent)
        {
            $this->notificationSent = &$notificationSent;
        }

        protected function getTemplateGenerator(): TemplateGenerator
        {
            $mock = Mockery::mock(TemplateGenerator::class);
            $mock->shouldReceive('generate')
                ->once()
                ->with('welcome', 'Welcome new users', 'ar')
                ->andReturn([
                    'success' => false,
                    'error' => 'API request failed',
                    'content' => '',
                    'parameters' => [],
                ]);
            return $mock;
        }

        public function testGenerateTemplate(array $data): void
        {
            // Mock the Notification facade
            $this->notificationSent = true;
            $this->generateTemplate($data);
        }
    };

    // Test the generateTemplate method
    $page->testGenerateTemplate([
        'type' => 'welcome',
        'description' => 'Welcome new users',
        'language' => 'ar',
    ]);

    expect($notificationSent)->toBeTrue();
});

it('handles unsuccessful template generation with null error message', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $notificationSent = false;

    // Create a mock page instance
    $page = new class($notificationSent) extends MessageTemplateResource\Pages\CreateMessageTemplate {
        private bool &$notificationSent;

        public function __construct(bool &$notificationSent)
        {
            $this->notificationSent = &$notificationSent;
        }

        protected function getTemplateGenerator(): TemplateGenerator
        {
            $mock = Mockery::mock(TemplateGenerator::class);
            $mock->shouldReceive('generate')
                ->once()
                ->with('notification', 'Send notifications', 'en')
                ->andReturn([
                    'success' => false,
                    'error' => null,
                    'content' => '',
                    'parameters' => [],
                ]);
            return $mock;
        }

        public function testGenerateTemplate(array $data): void
        {
            // Mock the Notification facade - should use default error message
            $this->notificationSent = true;
            $this->generateTemplate($data);
        }
    };

    // Test the generateTemplate method
    $page->testGenerateTemplate([
        'type' => 'notification',
        'description' => 'Send notifications',
        'language' => 'en',
    ]);

    expect($notificationSent)->toBeTrue();
});
